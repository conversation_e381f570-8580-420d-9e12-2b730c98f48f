/* Background slideshow styles */
.background-slideshow {
  position: relative;
  width: 100%;
  height: 100%;
}

.slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0;
  transition: opacity 2s ease-in-out;
}

.slide.active {
  opacity: 1;
}

/* Ensure proper z-index layering */
.relative {
  position: relative;
}

/* Additional styling for better text visibility */
.drop-shadow-lg {
  filter: drop-shadow(0 10px 8px rgb(0 0 0 / 0.04)) drop-shadow(0 4px 3px rgb(0 0 0 / 0.1));
}

.drop-shadow-md {
  filter: drop-shadow(0 4px 3px rgb(0 0 0 / 0.07)) drop-shadow(0 2px 2px rgb(0 0 0 / 0.06));
}

/* Tethering/Alive animation for LOGIN button */
.login-tether {
  animation: tether 2s ease-in-out infinite;
  transform-origin: center;
  border-width: 2px;
  border-style: solid;
  border-color: white;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

@keyframes tether {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    border-color: white;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  }
  25% {
    transform: scale(1.05) rotate(-1deg);
    border-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 6px 8px -1px rgb(0 0 0 / 0.15), 0 4px 6px -2px rgb(0 0 0 / 0.15);
  }
  50% {
    transform: scale(1.02) rotate(1deg);
    border-color: rgba(255, 255, 255, 0.8);
    box-shadow: 0 5px 7px -1px rgb(0 0 0 / 0.12), 0 3px 5px -2px rgb(0 0 0 / 0.12);
  }
  75% {
    transform: scale(1.08) rotate(-0.5deg);
    border-color: white;
    box-shadow: 0 8px 10px -1px rgb(0 0 0 / 0.2), 0 6px 8px -2px rgb(0 0 0 / 0.2);
  }
}

/* Hover effect enhancement */
.login-tether:hover {
  animation: tether-fast 0.8s ease-in-out infinite;
  border-color: rgba(255, 255, 255, 1) !important;
}

@keyframes tether-fast {
  0%, 100% {
    transform: scale(1.1) rotate(0deg);
    border-color: white;
    box-shadow: 0 8px 12px -1px rgb(0 0 0 / 0.25), 0 6px 10px -2px rgb(0 0 0 / 0.25);
  }
  25% {
    transform: scale(1.15) rotate(-2deg);
    border-color: rgba(255, 255, 255, 0.95);
    box-shadow: 0 10px 15px -1px rgb(0 0 0 / 0.3), 0 8px 12px -2px rgb(0 0 0 / 0.3);
  }
  50% {
    transform: scale(1.12) rotate(2deg);
    border-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 9px 13px -1px rgb(0 0 0 / 0.27), 0 7px 11px -2px rgb(0 0 0 / 0.27);
  }
  75% {
    transform: scale(1.18) rotate(-1deg);
    border-color: white;
    box-shadow: 0 12px 18px -1px rgb(0 0 0 / 0.35), 0 10px 15px -2px rgb(0 0 0 / 0.35);
  }
}

/* Mobile Navigation Pane Styles */
#mobile-nav-pane {
  backdrop-filter: blur(20px);
  border-left: 4px solid #f97316;
}

#mobile-nav-pane.open {
  transform: translateX(0);
}

#mobile-nav-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* Navigation link hover effects */
.nav-link {
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(214, 126, 126, 0.1), transparent);
  transition: left 0.5s;
}

.nav-link:hover::before {
  left: 100%;
}

/* Mobile menu button animation */
#mobile-menu-button {
  transition: transform 0.3s ease;
}

#mobile-menu-button.active {
  transform: rotate(90deg);
}

/* Responsive improvements */
@media (max-width: 600px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Ensure text doesn't overflow on small screens */
  h1, h2, h3 {
    word-wrap: break-word;
    hyphens: auto;
  }

  /* Improve button spacing on mobile */
  .login-tether {
    min-width: 120px;
    text-align: center;
  }
}

/* Smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}

/* Prevent horizontal scroll on mobile */
body {
  overflow-x: hidden;
}

/* Mobile navigation pane width adjustment for smaller screens */
@media (max-width: 480px) {
  #mobile-nav-pane {
    width: 100vw;
    max-width: 320px;
  }
}

/* Enhanced mobile menu button states */
#hamburger-icon.hidden {
  opacity: 0;
  transform: rotate(180deg);
}

#close-icon.show {
  opacity: 1;
  transform: rotate(0deg);
}

#close-icon {
  opacity: 0;
  transform: rotate(-180deg);
  transition: all 0.3s ease;
}

#hamburger-icon {
  transition: all 0.3s ease;
}

/* Footer Landscape/Horizontal Layout Enhancements */
footer {
  background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
}

/* Footer responsive improvements for landscape layout */
@media (max-width: 640px) {
  footer .flex-1 {
    width: 100%;
  }

  footer .flex-col.sm\\:flex-row {
    flex-direction: column;
    align-items: flex-start;
  }

  footer .sm\\:space-x-8 > * + * {
    margin-left: 0;
    margin-top: 0.5rem;
  }
}

/* Enhanced footer section spacing */
footer h3 {
  border-bottom: 2px solid #f97316;
  padding-bottom: 0.5rem;
  margin-bottom: 1rem;
  display: inline-block;
}

/* Footer hover effects */
footer a:hover {
  transform: translateY(-1px);
  transition: all 0.3s ease;
}

/* Footer social icons enhanced hover */
footer .bg-gray-800:hover {
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}